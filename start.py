#!/usr/bin/env python3
"""
Startup script for Claims Data Q&A API
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import fastapi
        import uvicorn
        import chromadb
        import sentence_transformers
        import openai
        print("✓ All required packages are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing package: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists and has required variables"""
    env_file = Path(".env")
    if not env_file.exists():
        print("✗ .env file not found")
        print("Please copy .env.example to .env and configure your settings")
        return False
    
    # Check for required variables
    required_vars = ["OPENAI_API_KEY"]
    missing_vars = []
    
    with open(env_file, 'r') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content or f"{var}=your_" in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ Missing or unconfigured environment variables: {', '.join(missing_vars)}")
        print("Please configure these in your .env file")
        return False
    
    print("✓ Environment configuration looks good")
    return True

def check_sample_data():
    """Check if sample data file exists"""
    sample_file = Path("response.json")
    if not sample_file.exists():
        print("⚠ Sample data file 'response.json' not found")
        print("You can still run the API, but you'll need to upload claims data")
        return False
    
    try:
        with open(sample_file, 'r') as f:
            data = json.load(f)
            if 'data' in data and len(data['data']) > 0:
                print("✓ Sample data file is valid")
                return True
            else:
                print("⚠ Sample data file exists but appears to be empty or invalid")
                return False
    except json.JSONDecodeError:
        print("⚠ Sample data file exists but contains invalid JSON")
        return False

def create_directories():
    """Create necessary directories"""
    dirs = ["chroma_db", "logs"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    print("✓ Created necessary directories")

def start_server():
    """Start the FastAPI server"""
    print("\n" + "="*50)
    print("Starting Claims Data Q&A API Server...")
    print("="*50)
    
    try:
        # Import here to ensure all checks pass first
        import uvicorn
        from config import settings
        
        print(f"Server will start on: http://{settings.API_HOST}:{settings.API_PORT}")
        print("API Documentation: http://localhost:8000/docs")
        print("Health Check: http://localhost:8000/health")
        print("\nPress Ctrl+C to stop the server")
        print("-"*50)
        
        uvicorn.run(
            "main:app",
            host=settings.API_HOST,
            port=settings.API_PORT,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\n✗ Error starting server: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("Claims Data Q&A API - Startup Check")
    print("="*40)
    
    # Run all checks
    checks_passed = True
    
    if not check_requirements():
        checks_passed = False
    
    if not check_env_file():
        checks_passed = False
    
    check_sample_data()  # This is optional
    
    if not checks_passed:
        print("\n✗ Some checks failed. Please fix the issues above before starting.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    print("\n✓ All checks passed!")
    
    # Ask user if they want to start the server
    try:
        response = input("\nStart the server now? (y/n): ").lower().strip()
        if response in ['y', 'yes', '']:
            start_server()
        else:
            print("Server not started. Run 'python main.py' when ready.")
    except KeyboardInterrupt:
        print("\nStartup cancelled by user")

if __name__ == "__main__":
    main()
