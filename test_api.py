import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Health Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_upload_claims():
    """Test uploading claims data"""
    print("Testing claims data upload...")
    
    # Load the response.json file
    with open('response.json', 'r') as f:
        claims_data = json.load(f)
    
    response = requests.post(f"{BASE_URL}/upload-claims-json", json=claims_data)
    print(f"Upload Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    
    return response.status_code == 200

def test_stats():
    """Test stats endpoint"""
    print("Testing stats endpoint...")
    response = requests.get(f"{BASE_URL}/stats")
    print(f"Stats Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_questions():
    """Test asking questions"""
    print("Testing Q&A functionality...")
    
    questions = [
        "What is the claim number?",
        "What was the total approved amount?",
        "What deductions were applied?",
        "Which rules were executed?",
        "What was the financial impact?"
    ]
    
    for question in questions:
        print(f"\nAsking: {question}")
        response = requests.post(f"{BASE_URL}/ask", json={
            "question": question,
            "max_results": 3
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"Answer: {result['answer'][:200]}...")
            print(f"Confidence: {result.get('confidence_score', 'N/A')}")
            print(f"Relevant chunks: {len(result.get('relevant_chunks', []))}")
        else:
            print(f"Error: {response.status_code} - {response.text}")
        
        time.sleep(1)  # Rate limiting
    
    print("-" * 50)

def test_claim_summary():
    """Test claim summary"""
    print("Testing claim summary...")
    
    # Use the claim number from the sample data
    claim_no = 131420765
    
    response = requests.get(f"{BASE_URL}/claim/{claim_no}/summary")
    print(f"Summary Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Claim: {result['claim_no']}")
        print(f"Summary: {result['summary'][:300]}...")
    else:
        print(f"Error: {response.text}")
    
    print("-" * 50)

def test_suggested_questions():
    """Test suggested questions"""
    print("Testing suggested questions...")
    
    claim_no = 131420765
    
    response = requests.get(f"{BASE_URL}/claim/{claim_no}/questions")
    print(f"Suggested Questions Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Suggested questions for claim {claim_no}:")
        for i, question in enumerate(result['suggested_questions'][:5], 1):
            print(f"{i}. {question}")
    else:
        print(f"Error: {response.text}")
    
    print("-" * 50)

def main():
    """Run all tests"""
    print("Starting API Tests...")
    print("=" * 50)
    
    # Test health
    test_health()
    
    # Test upload
    upload_success = test_upload_claims()
    
    if upload_success:
        # Wait a moment for processing
        time.sleep(2)
        
        # Test other endpoints
        test_stats()
        test_questions()
        test_claim_summary()
        test_suggested_questions()
    else:
        print("Upload failed, skipping other tests")
    
    print("Tests completed!")

if __name__ == "__main__":
    main()
