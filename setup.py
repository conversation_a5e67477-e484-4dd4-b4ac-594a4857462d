#!/usr/bin/env python3
"""
Complete setup script for Claims Data Q&A API
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_step(step_num, title):
    """Print a formatted step"""
    print(f"\n[Step {step_num}] {title}")
    print("-" * 40)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("✗ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python version: {sys.version.split()[0]}")
    return True

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing packages: {e}")
        return False

def setup_environment():
    """Setup environment configuration"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✓ .env file already exists")
        return True
    
    if not env_example.exists():
        print("✗ .env.example file not found")
        return False
    
    # Copy example to .env
    with open(env_example, 'r') as src, open(env_file, 'w') as dst:
        content = src.read()
        dst.write(content)
    
    print("✓ Created .env file from template")
    print("⚠ Please edit .env file and add your OpenAI API key")
    
    return True

def create_directories():
    """Create necessary directories"""
    dirs = ["chroma_db", "logs", "static"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    print("✓ Created necessary directories")

def check_sample_data():
    """Check if sample data exists"""
    sample_file = Path("response.json")
    if sample_file.exists():
        try:
            with open(sample_file, 'r') as f:
                data = json.load(f)
                if 'data' in data and len(data['data']) > 0:
                    print("✓ Sample data file is valid")
                    return True
        except:
            pass
    
    print("⚠ Sample data file not found or invalid")
    print("  You can still run the system, but you'll need to upload claims data")
    return False

def test_imports():
    """Test if all required modules can be imported"""
    required_modules = [
        'fastapi', 'uvicorn', 'chromadb', 'sentence_transformers', 
        'openai', 'pydantic', 'python_multipart'
    ]
    
    failed_imports = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            failed_imports.append(module)
    
    if failed_imports:
        print(f"✗ Failed to import: {', '.join(failed_imports)}")
        return False
    
    print("✓ All required modules can be imported")
    return True

def create_startup_scripts():
    """Create convenient startup scripts"""
    
    # Windows batch file
    bat_content = """@echo off
echo Starting Claims Data Q&A API...
python main.py
pause
"""
    
    with open("start.bat", "w") as f:
        f.write(bat_content)
    
    # Unix shell script
    sh_content = """#!/bin/bash
echo "Starting Claims Data Q&A API..."
python3 main.py
"""
    
    with open("start.sh", "w") as f:
        f.write(sh_content)
    
    # Make shell script executable
    try:
        os.chmod("start.sh", 0o755)
    except:
        pass
    
    print("✓ Created startup scripts (start.bat, start.sh)")

def main():
    """Main setup function"""
    print_header("Claims Data Q&A API - Setup")
    
    print("This script will set up the Claims Data Q&A API system.")
    print("It will install dependencies, configure the environment, and prepare the system.")
    
    # Step 1: Check Python version
    print_step(1, "Checking Python Version")
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Install requirements
    print_step(2, "Installing Dependencies")
    if not install_requirements():
        print("\nTry running manually: pip install -r requirements.txt")
        sys.exit(1)
    
    # Step 3: Test imports
    print_step(3, "Testing Module Imports")
    if not test_imports():
        print("\nSome modules failed to import. Please check the installation.")
        sys.exit(1)
    
    # Step 4: Setup environment
    print_step(4, "Setting Up Environment")
    setup_environment()
    
    # Step 5: Create directories
    print_step(5, "Creating Directories")
    create_directories()
    
    # Step 6: Check sample data
    print_step(6, "Checking Sample Data")
    has_sample_data = check_sample_data()
    
    # Step 7: Create startup scripts
    print_step(7, "Creating Startup Scripts")
    create_startup_scripts()
    
    # Final instructions
    print_header("Setup Complete!")
    
    print("✓ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit the .env file and add your OpenAI API key:")
    print("   OPENAI_API_KEY=your_actual_api_key_here")
    print("\n2. Start the server:")
    print("   python main.py")
    print("   or")
    print("   python start.py")
    print("   or use the startup scripts (start.bat on Windows, ./start.sh on Unix)")
    
    if has_sample_data:
        print("\n3. Upload sample data (after starting the server):")
        print("   python upload_sample_data.py")
    
    print("\n4. Access the system:")
    print("   - API Documentation: http://localhost:8000/docs")
    print("   - Web Interface: http://localhost:8000/static/index.html")
    print("   - Health Check: http://localhost:8000/health")
    
    print("\nExample API usage:")
    print("   curl -X POST http://localhost:8000/ask \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"question\": \"What was the total approved amount?\"}'")
    
    print("\n" + "=" * 60)
    print("Happy analyzing! 🚀")

if __name__ == "__main__":
    main()
